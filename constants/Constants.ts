//export const API_URL = 'http://127.0.0.1:8000';
export const API_URL = 'https://94b4yj9as6.execute-api.us-east-1.amazonaws.com/api';

export const R2_URL = 'https://assets.taleai.app'
export const R2_URL_STORY_IMAGES = `${R2_URL}/story_image`
export const R2_URL_STORY_READINGS = `${R2_URL}/story_reading`

export const S3_URL = 'https://dofatech-storyai.s3.amazonaws.com';
export const S3_URL_STORY_IMAGES =
	'https://dofatech-storyai.s3.amazonaws.com/story_image';

export const MINI_PLAYER_HEIGHT = 60;

// TODO: Migrate to Superwall - Add SUPERWALL_API_KEY here
// export const ADAPTY_PUBLIC_KEY = 'public_live_ghBGCZRL.jDrrODc9VWVInGdgUPsE';

export const PRIVACY_POLICY_URL = 'https://storyai-files.dofatech.com/legal/privacy_policy.html';
export const TERMS_OF_SERVICE_URL = 'https://www.apple.com/legal/internet-services/itunes/dev/stdeula';

export const APP_STORE_ID = '6627341893'
export const IOS_WRITE_REVIEW_URL = `itms-apps://itunes.apple.com/app/viewContentsUserReviews/id${APP_STORE_ID}?action=write-review`
export const IOS_WRITE_REVIEW_URL_BROWSER = `https://apps.apple.com/app/apple-store/id${APP_STORE_ID}?action=write-review`
export const SLACK_FEEDBACK_URL = '*********************************************************************************'

export const getClonePreviewUrl = (voiceId: number | string) => {
	return `https://dofatech-storyai.s3.us-east-1.amazonaws.com/voice_preview/voice_preview_${voiceId}.mp3`;
}